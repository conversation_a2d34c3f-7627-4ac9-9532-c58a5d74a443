import React, { useEffect, useState } from 'react';
import { FormInstance, Input, Tabs } from 'antd';

const { TextArea } = Input;

interface RulesStepProps {
  form: FormInstance;
  stepData: any;
  mdValue: string;
  setMdValue: (value: string) => void;
}

interface LanguageRules {
  en: string;
  zh: string;
  ko: string;
}

// 语言映射
const LANGUAGE_LABELS = {
  en: '🇺🇸 English',
  zh: '🇨🇳 中文',
  ko: '🇰🇷 한국어',
};

// 缓存键名
const CACHE_KEY = 'event_rules_cache';

// 解析规则内容，从拼接格式中提取各语言内容
const parseRulesContent = (rulesContent: string): LanguageRules => {
  const defaultRules = { en: '', zh: '', ko: '' };

  if (!rulesContent) return defaultRules;

  // 如果包含分隔符，说明是新格式
  if (rulesContent.includes('predictionOne$')) {
    const enMatch = rulesContent.match(/predictionOne\$en(.*?)(?=predictionOne\$|$)/s);
    const zhMatch = rulesContent.match(/predictionOne\$zh(.*?)(?=predictionOne\$|$)/s);
    const koMatch = rulesContent.match(/predictionOne\$ko(.*?)(?=predictionOne\$|$)/s);

    return {
      en: enMatch ? enMatch[1].trim() : '',
      zh: zhMatch ? zhMatch[1].trim() : '',
      ko: koMatch ? koMatch[1].trim() : '',
    };
  }

  // 旧格式，放到英文中
  return { ...defaultRules, en: rulesContent };
};

// 拼接规则内容为提交格式
const combineRulesContent = (rules: LanguageRules): string => {
  const parts = [];
  if (rules.en.trim()) parts.push(`predictionOne$en${rules.en.trim()}`);
  if (rules.zh.trim()) parts.push(`predictionOne$zh${rules.zh.trim()}`);
  if (rules.ko.trim()) parts.push(`predictionOne$ko${rules.ko.trim()}`);
  return parts.join('');
};

const RulesStep: React.FC<RulesStepProps> = ({ form, stepData, mdValue, setMdValue }) => {
  const [languageRules, setLanguageRules] = useState<LanguageRules>({ en: '', zh: '', ko: '' });
  const [selectedLanguages, setSelectedLanguages] = useState<string[]>(['en']);
  const [activeTab, setActiveTab] = useState<string>('en');

  // 从浏览器缓存加载数据
  const loadFromCache = (): LanguageRules | null => {
    try {
      const cached = localStorage.getItem(CACHE_KEY);
      return cached ? JSON.parse(cached) : null;
    } catch {
      return null;
    }
  };

  // 保存到浏览器缓存
  const saveToCache = (rules: LanguageRules) => {
    try {
      localStorage.setItem(CACHE_KEY, JSON.stringify(rules));
    } catch {
      // 忽略缓存错误
    }
  };

  // 初始化数据
  useEffect(() => {
    // 获取选中的语言
    const languages = stepData.languages || form.getFieldValue('languages') || ['en'];
    setSelectedLanguages(languages);

    // 设置默认活跃标签
    if (languages.length > 0 && !languages.includes(activeTab)) {
      setActiveTab(languages[0]);
    }

    // 优先从缓存加载，然后从stepData加载
    const cachedRules = loadFromCache();
    let initialRules: LanguageRules;

    if (cachedRules) {
      initialRules = cachedRules;
    } else if (stepData.rules || stepData.mdValue) {
      const rulesContent = stepData.mdValue || stepData.rules || '';
      initialRules = parseRulesContent(rulesContent);
    } else {
      initialRules = { en: '', zh: '', ko: '' };
    }

    setLanguageRules(initialRules);

    // 更新表单和父组件状态
    const combinedRules = combineRulesContent(initialRules);
    setMdValue(combinedRules);
    form.setFieldsValue({ rules: combinedRules });
  }, [stepData, form, setMdValue, activeTab]);

  // 处理单个语言规则变化
  const handleLanguageRuleChange = (language: string, value: string) => {
    const newRules = { ...languageRules, [language]: value };
    setLanguageRules(newRules);

    // 保存到缓存
    saveToCache(newRules);

    // 更新表单和父组件状态
    const combinedRules = combineRulesContent(newRules);
    setMdValue(combinedRules);
    form.setFieldsValue({ rules: combinedRules });

    // 手动触发字段验证
    form.validateFields(['rules']).catch(() => {
      // 忽略验证错误，只是为了触发验证状态更新
    });
  };

  // 默认规则模板（多语言格式）
  const defaultTemplates = {
    en: `Event Rules

Participation Guidelines
All participants must follow community guidelines
Predictions must be made before the event deadline
Only one prediction per user is allowed

Scoring System
Points are awarded based on prediction accuracy
Bonus points may be given for early predictions
Final scores are calculated after event conclusion

Important Notes
Event organizers reserve the right to modify rules if necessary
Disputes will be resolved by the moderation team
Have fun and predict responsibly!

Last updated: ${new Date().toLocaleDateString()}`,
    zh: `活动规则

参与指南
所有参与者必须遵守社区准则
必须在活动截止时间前进行预测
每位用户只能进行一次预测

评分系统
根据预测准确性给予积分
提前预测可能获得额外积分
活动结束后计算最终得分

重要说明
活动组织者保留在必要时修改规则的权利
争议将由管理团队解决
祝您预测愉快，负责任地参与！

最后更新：${new Date().toLocaleDateString()}`,
    ko: `이벤트 규칙

참여 가이드라인
모든 참가자는 커뮤니티 가이드라인을 준수해야 합니다
이벤트 마감일 전에 예측을 해야 합니다
사용자당 하나의 예측만 허용됩니다

점수 시스템
예측 정확도에 따라 점수가 부여됩니다
조기 예측에 대해 보너스 점수가 주어질 수 있습니다
이벤트 종료 후 최종 점수가 계산됩니다

중요 사항
이벤트 주최자는 필요시 규칙을 수정할 권리를 보유합니다
분쟁은 관리팀에서 해결합니다
즐겁게 예측하고 책임감 있게 참여하세요!

마지막 업데이트: ${new Date().toLocaleDateString()}`
  };

  const handleUseTemplate = async () => {
    // 导入 stripMarkdown 函数以确保模板内容是纯文本
    const { stripMarkdown } = await import('@/utils');

    const newRules: LanguageRules = {
      en: stripMarkdown(defaultTemplates.en),
      zh: stripMarkdown(defaultTemplates.zh),
      ko: stripMarkdown(defaultTemplates.ko),
    };

    setLanguageRules(newRules);
    saveToCache(newRules);

    // 更新表单和父组件状态
    const combinedRules = combineRulesContent(newRules);
    setMdValue(combinedRules);
    form.setFieldsValue({ rules: combinedRules });
  };

  return (
    <>
      {/* 添加CSS动画样式 */}
      <style>
        {`
          @keyframes slideInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `}
      </style>

      <div style={{ width: '96%', margin: '0 auto' }}>
        {/* 整体表单容器 */}
        <div
          style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            border: '2px solid #e2e8f0',
            borderRadius: '24px',
            padding: '40px',
            boxShadow: '0 12px 32px rgba(0, 0, 0, 0.08)',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* 装饰性背景元素 */}
          <div
            style={{
              position: 'absolute',
              top: '-40px',
              right: '-40px',
              width: '120px',
              height: '120px',
              background: 'linear-gradient(135deg, #f59e0b, #d97706)',
              borderRadius: '50%',
              opacity: '0.04',
            }}
          ></div>
          <div
            style={{
              position: 'absolute',
              bottom: '-50px',
              left: '-50px',
              width: '150px',
              height: '150px',
              background: 'linear-gradient(135deg, #ef4444, #dc2626)',
              borderRadius: '50%',
              opacity: '0.03',
            }}
          ></div>

          {/* 表单标题 */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
              marginBottom: '20px',
              paddingBottom: '20px',
              borderBottom: '3px solid #e2e8f0',
              position: 'relative',
              zIndex: 1,
            }}
          >
            <div
              style={{
                width: '56px',
                height: '56px',
                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                borderRadius: '18px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '24px',
                boxShadow: '0 6px 16px rgba(245, 158, 11, 0.3)',
              }}
            >
              📋
            </div>
            <div>
              <div
                style={{
                  fontSize: '28px',
                  fontWeight: '800',
                  color: '#1f2937',
                  marginBottom: '6px',
                  letterSpacing: '0.4px',
                }}
              >
                Event Rules
              </div>
              <div
                style={{
                  fontSize: '16px',
                  color: '#6b7280',
                  fontWeight: '500',
                }}
              >
                Define participation guidelines and terms
              </div>
            </div>
          </div>

          {/* 表单内容 */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '32px',
              position: 'relative',
              zIndex: 1,
            }}
          >
            {/* 多语言提示和模板按钮 */}
            <div
              style={{
                background: 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)',
                border: '2px solid #3b82f6',
                borderRadius: '16px',
                padding: '20px',
                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.15)',
                position: 'relative',
                overflow: 'hidden',
                animation: 'slideInUp 0.6s ease-out',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              {/* 装饰性背景元素 */}
              <div
                style={{
                  position: 'absolute',
                  top: '-10px',
                  right: '-10px',
                  width: '60px',
                  height: '60px',
                  background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                  borderRadius: '50%',
                  opacity: '0.1',
                }}
              ></div>

              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  position: 'relative',
                  zIndex: 1,
                }}
              >
                <div>
                  <div
                    style={{
                      fontSize: '18px',
                      fontWeight: '700',
                      color: '#1e40af',
                      marginBottom: '4px',
                      letterSpacing: '0.3px',
                    }}
                  >
                    Multi-language Rules
                  </div>
                  <div
                    style={{
                      fontSize: '16px',
                      color: '#1e40af',
                      fontWeight: '500',
                      opacity: '0.9',
                    }}
                  >
                    Create rules for each selected language
                  </div>
                </div>
              </div>

              <button
                onClick={handleUseTemplate}
                style={{
                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '10px',
                  padding: '12px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
                  transition: 'all 0.2s ease',
                  position: 'relative',
                  zIndex: 1,
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = 'translateY(-2px)';
                  target.style.boxShadow = '0 6px 16px rgba(59, 130, 246, 0.4)';
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = 'translateY(0)';
                  target.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';
                }}
              >
                📝 Use Template
              </button>
            </div>

            {/* 规则编辑器 */}
            <div>
              <div style={{ marginBottom: '24px' }}>
                <div
                  style={{
                    fontSize: '24px',
                    fontWeight: '700',
                    color: '#1f2937',
                    letterSpacing: '0.3px',
                    marginBottom: '16px',
                  }}
                >
                  📋 Rules
                </div>

                {/* 多语言标签页 */}
                <Tabs
                  activeKey={activeTab}
                  onChange={setActiveTab}
                  type="card"
                  style={{ marginBottom: '16px' }}
                  items={selectedLanguages.map(lang => ({
                    key: lang,
                    label: LANGUAGE_LABELS[lang as keyof typeof LANGUAGE_LABELS] || lang.toUpperCase(),
                    children: (
                      <div
                        style={{
                          border: '2px solid #e5e7eb',
                          borderRadius: '12px',
                          overflow: 'hidden',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
                        }}
                      >
                        <TextArea
                          value={languageRules[lang as keyof LanguageRules] || ''}
                          onChange={(e) => handleLanguageRuleChange(lang, e.target.value)}
                          placeholder={`Enter event rules in ${LANGUAGE_LABELS[lang as keyof typeof LANGUAGE_LABELS] || lang}...`}
                          autoSize={{ minRows: 12, maxRows: 20 }}
                          style={{
                            fontSize: '14px',
                            lineHeight: '1.6',
                            fontFamily: 'monospace',
                            border: 'none',
                            borderRadius: '12px',
                            padding: '16px',
                            resize: 'vertical',
                            backgroundColor: 'white',
                          }}
                        />
                      </div>
                    ),
                  }))}
                />
              </div>

              <p
                style={{
                  fontSize: '16px',
                  color: '#6b7280',
                  fontWeight: '500',
                  fontStyle: 'italic',
                  margin: 0,
                }}
              >
                Define participation guidelines, scoring system, and important terms for each language
              </p>
            </div>

            {/* 重要警告 */}
            <div
              style={{
                background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
                border: '2px solid #f59e0b',
                borderRadius: '16px',
                padding: '16px',
                boxShadow: '0 4px 12px rgba(245, 158, 11, 0.15)',
                position: 'relative',
                overflow: 'hidden',
                animation: 'slideInUp 0.8s ease-out',
                textAlign: 'center',
              }}
            >
              {/* 装饰性背景元素 */}
              <div
                style={{
                  position: 'absolute',
                  top: '-10px',
                  right: '-10px',
                  width: '60px',
                  height: '60px',
                  background: 'linear-gradient(135deg, #f59e0b, #d97706)',
                  borderRadius: '50%',
                  opacity: '0.1',
                }}
              ></div>

              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                  zIndex: 1,
                }}
              >
                <div>
                  <div
                    style={{
                      fontSize: '18px',
                      fontWeight: '700',
                      color: '#92400e',
                      marginBottom: '4px',
                      letterSpacing: '0.3px',
                    }}
                  >
                    Important Notice
                  </div>
                  <div
                    style={{
                      fontSize: '16px',
                      color: '#92400e',
                      fontWeight: '500',
                      opacity: '0.9',
                    }}
                  >
                    Rules can't be changed after users start participating
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default RulesStep;
